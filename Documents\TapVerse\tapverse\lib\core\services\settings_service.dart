import 'package:shared_preferences/shared_preferences.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

class SettingsService {
  static final SettingsService _instance = SettingsService._internal();
  factory SettingsService() => _instance;
  SettingsService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Settings keys
  static const String _soundEnabledKey = 'sound_enabled';
  static const String _musicEnabledKey = 'music_enabled';
  static const String _vibrationEnabledKey = 'vibration_enabled';
  static const String _soundVolumeKey = 'sound_volume';
  static const String _musicVolumeKey = 'music_volume';
  static const String _notificationsEnabledKey = 'notifications_enabled';
  static const String _darkModeKey = 'dark_mode';
  static const String _autoLoginEnabledKey = 'auto_login_enabled';

  // Default values
  static const bool _defaultSoundEnabled = true;
  static const bool _defaultMusicEnabled = true;
  static const bool _defaultVibrationEnabled = true;
  static const double _defaultSoundVolume = 1.0;
  static const double _defaultMusicVolume = 0.7;
  static const bool _defaultNotificationsEnabled = true;
  static const bool _defaultDarkMode = true;
  static const bool _defaultAutoLoginEnabled = true;

  // Getters for current settings
  bool _soundEnabled = _defaultSoundEnabled;
  bool _musicEnabled = _defaultMusicEnabled;
  bool _vibrationEnabled = _defaultVibrationEnabled;
  double _soundVolume = _defaultSoundVolume;
  double _musicVolume = _defaultMusicVolume;
  bool _notificationsEnabled = _defaultNotificationsEnabled;
  bool _darkMode = _defaultDarkMode;
  bool _autoLoginEnabled = _defaultAutoLoginEnabled;

  bool get soundEnabled => _soundEnabled;
  bool get musicEnabled => _musicEnabled;
  bool get vibrationEnabled => _vibrationEnabled;
  double get soundVolume => _soundVolume;
  double get musicVolume => _musicVolume;
  bool get notificationsEnabled => _notificationsEnabled;
  bool get darkMode => _darkMode;
  bool get autoLoginEnabled => _autoLoginEnabled;

  Future<void> initialize() async {
    await _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      _soundEnabled = prefs.getBool(_soundEnabledKey) ?? _defaultSoundEnabled;
      _musicEnabled = prefs.getBool(_musicEnabledKey) ?? _defaultMusicEnabled;
      _vibrationEnabled = prefs.getBool(_vibrationEnabledKey) ?? _defaultVibrationEnabled;
      _soundVolume = prefs.getDouble(_soundVolumeKey) ?? _defaultSoundVolume;
      _musicVolume = prefs.getDouble(_musicVolumeKey) ?? _defaultMusicVolume;
      _notificationsEnabled = prefs.getBool(_notificationsEnabledKey) ?? _defaultNotificationsEnabled;
      _darkMode = prefs.getBool(_darkModeKey) ?? _defaultDarkMode;
      _autoLoginEnabled = prefs.getBool(_autoLoginEnabledKey) ?? _defaultAutoLoginEnabled;
    } catch (e) {
      print('Error loading settings: $e');
    }
  }

  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      await prefs.setBool(_soundEnabledKey, _soundEnabled);
      await prefs.setBool(_musicEnabledKey, _musicEnabled);
      await prefs.setBool(_vibrationEnabledKey, _vibrationEnabled);
      await prefs.setDouble(_soundVolumeKey, _soundVolume);
      await prefs.setDouble(_musicVolumeKey, _musicVolume);
      await prefs.setBool(_notificationsEnabledKey, _notificationsEnabled);
      await prefs.setBool(_darkModeKey, _darkMode);
      await prefs.setBool(_autoLoginEnabledKey, _autoLoginEnabled);
    } catch (e) {
      print('Error saving settings: $e');
    }
  }

  // Sound settings
  Future<void> setSoundEnabled(bool enabled) async {
    _soundEnabled = enabled;
    await _saveSettings();
  }

  Future<void> setMusicEnabled(bool enabled) async {
    _musicEnabled = enabled;
    await _saveSettings();
  }

  Future<void> setSoundVolume(double volume) async {
    _soundVolume = volume.clamp(0.0, 1.0);
    await _saveSettings();
  }

  Future<void> setMusicVolume(double volume) async {
    _musicVolume = volume.clamp(0.0, 1.0);
    await _saveSettings();
  }

  // Vibration settings
  Future<void> setVibrationEnabled(bool enabled) async {
    _vibrationEnabled = enabled;
    await _saveSettings();
  }

  // Notification settings
  Future<void> setNotificationsEnabled(bool enabled) async {
    _notificationsEnabled = enabled;
    await _saveSettings();
  }

  // Theme settings
  Future<void> setDarkMode(bool enabled) async {
    _darkMode = enabled;
    await _saveSettings();
  }

  // Auto-login settings
  Future<void> setAutoLoginEnabled(bool enabled) async {
    _autoLoginEnabled = enabled;
    await _saveSettings();
  }

  // Reset all settings to defaults
  Future<void> resetToDefaults() async {
    _soundEnabled = _defaultSoundEnabled;
    _musicEnabled = _defaultMusicEnabled;
    _vibrationEnabled = _defaultVibrationEnabled;
    _soundVolume = _defaultSoundVolume;
    _musicVolume = _defaultMusicVolume;
    _notificationsEnabled = _defaultNotificationsEnabled;
    _darkMode = _defaultDarkMode;
    
    await _saveSettings();
  }

  // Reset user data (dangerous operation)
  Future<bool> resetUserData() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      // Delete user document
      await _firestore.collection('users').doc(user.uid).delete();
      
      // Delete user's inventory
      final inventoryQuery = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('inventory')
          .get();
      
      for (final doc in inventoryQuery.docs) {
        await doc.reference.delete();
      }

      // Delete user's leaderboard entries
      final gamesCollection = await _firestore.collection('leaderboards').get();
      for (final gameDoc in gamesCollection.docs) {
        final userScoreDoc = await gameDoc.reference
            .collection('scores')
            .doc(user.uid)
            .get();
        
        if (userScoreDoc.exists) {
          await userScoreDoc.reference.delete();
        }
      }

      // Reset local settings
      await resetToDefaults();
      
      return true;
    } catch (e) {
      print('Error resetting user data: $e');
      return false;
    }
  }

  // Export settings as a map
  Map<String, dynamic> exportSettings() {
    return {
      'soundEnabled': _soundEnabled,
      'musicEnabled': _musicEnabled,
      'vibrationEnabled': _vibrationEnabled,
      'soundVolume': _soundVolume,
      'musicVolume': _musicVolume,
      'notificationsEnabled': _notificationsEnabled,
      'darkMode': _darkMode,
    };
  }

  // Import settings from a map
  Future<void> importSettings(Map<String, dynamic> settings) async {
    _soundEnabled = settings['soundEnabled'] ?? _defaultSoundEnabled;
    _musicEnabled = settings['musicEnabled'] ?? _defaultMusicEnabled;
    _vibrationEnabled = settings['vibrationEnabled'] ?? _defaultVibrationEnabled;
    _soundVolume = (settings['soundVolume'] ?? _defaultSoundVolume).toDouble();
    _musicVolume = (settings['musicVolume'] ?? _defaultMusicVolume).toDouble();
    _notificationsEnabled = settings['notificationsEnabled'] ?? _defaultNotificationsEnabled;
    _darkMode = settings['darkMode'] ?? _defaultDarkMode;
    
    await _saveSettings();
  }
}
